import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useTranslation, type Language } from "@/lib/i18n";
import { SEOHead } from "@/components/SEOHead";
import { Shield, Eye, Lock, UserCheck, FileText, Mail, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface PrivacyProps {
  language: Language;
}

export default function Privacy({ language }: PrivacyProps) {
  const { t } = useTranslation(language);

  const sections = [
    {
      id: "collection",
      title: t("privacy.collection.title"),
      icon: <Eye className="h-6 w-6" />,
      content: t("privacy.collection.content")
    },
    {
      id: "usage",
      title: t("privacy.usage.title"),
      icon: <UserCheck className="h-6 w-6" />,
      content: t("privacy.usage.content")
    },
    {
      id: "sharing",
      title: t("privacy.sharing.title"),
      icon: <Shield className="h-6 w-6" />,
      content: t("privacy.sharing.content")
    },
    {
      id: "security",
      title: t("privacy.security.title"),
      icon: <Lock className="h-6 w-6" />,
      content: t("privacy.security.content")
    },
    {
      id: "retention",
      title: t("privacy.retention.title"),
      icon: <FileText className="h-6 w-6" />,
      content: t("privacy.retention.content")
    },
    {
      id: "rights",
      title: t("privacy.rights.title"),
      icon: <Mail className="h-6 w-6" />,
      content: t("privacy.rights.content")


  ];

  return (
    <>
      <SEOHead
        title={`${t("privacy.page.title")} - Remove.bg`}
        description={t("privacy.page.description")}
        language={language}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Back Button */}
            <div className="mb-8">
              <Link href="/">
                <Button variant="ghost" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  {t("privacy.back_home")}
                </Button>
              </Link>
            </div>
            <div className="text-center mb-16">
              <div className="gradient-bg rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Shield className="h-10 w-10 text-white" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                {t("privacy.page.title")}
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t("privacy.page.description")}
              </p>
              <div className="mt-6 text-sm text-gray-500">
                <p>{t("privacy.last_updated")}</p>
                <p>{t("privacy.effective_date")}</p>
              </div>
            </div>
          </div>
        </section>

        {/* Privacy Sections */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Quick Overview */}
            <Card className="mb-12 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl gradient-text">{t("privacy.summary.title")}</CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600 space-y-4">
                <p>
                  {t("privacy.summary.description")}
                </p>
              </CardContent>
            </Card>

            {/* Detailed Sections */}
            <div className="space-y-8">
              {sections.map((section, index) => (
                <Card key={section.id} className="shadow-lg">
                  <CardHeader>
                    <div className="flex items-center mb-4">
                      <div className="gradient-bg rounded-lg p-3 text-white mr-4">
                        {section.icon}
                      </div>
                      <CardTitle className="text-xl text-gray-900">
                        {index + 1}. {section.title}
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-gray-600 whitespace-pre-line leading-relaxed">
                      {section.content}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* GDPR Notice */}
            <Card className="mt-12 shadow-lg border-l-4 border-blue-500">
              <CardHeader>
                <CardTitle className="text-xl text-blue-700">
                  GDPR Compliance Notice
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  For users in the European Union, we comply with the General Data Protection Regulation (GDPR). 
                  You have additional rights under GDPR, including the right to data portability and the right to 
                  lodge a complaint with a supervisory authority.
                </p>
                <p className="mt-4">
                  Our lawful basis for processing your data is typically:
                </p>
                <ul className="list-disc pl-6 mt-2 space-y-1">
                  <li>Consent: When you voluntarily provide information</li>
                  <li>Contract: To provide our services</li>
                  <li>Legitimate interests: To improve our service and prevent fraud</li>
                </ul>
              </CardContent>
            </Card>

            {/* California Privacy Rights */}
            <Card className="mt-8 shadow-lg border-l-4 border-purple-500">
              <CardHeader>
                <CardTitle className="text-xl text-purple-700">
                  California Privacy Rights (CCPA)
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  California residents have specific rights under the California Consumer Privacy Act (CCPA):
                </p>
                <ul className="list-disc pl-6 mt-2 space-y-1">
                  <li>Right to know what personal information is collected</li>
                  <li>Right to delete personal information</li>
                  <li>Right to opt-out of the sale of personal information</li>
                  <li>Right to non-discrimination for exercising privacy rights</li>
                </ul>
                <p className="mt-4">
                  <strong>Note:</strong> We do not sell personal information as defined by the CCPA.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </>
  );
}
