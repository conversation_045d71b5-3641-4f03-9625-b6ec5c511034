{"hash": "b19a5d99", "configHash": "9eb1d34c", "lockfileHash": "43edb153", "browserHash": "192171cd", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "fe91660c", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "4bad59e2", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "6a4c4040", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "e3e2f61c", "needsInterop": true}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "3a82af9b", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "d2f42fd0", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "6673edf3", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "4e5ed010", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "07ce91a7", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "3307dd5f", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "a1e2ce71", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "3b852a1a", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "ade0c4db", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "37171a0b", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "c4ad14cc", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "05117322", "needsInterop": false}, "wouter": {"src": "../../wouter/esm/index.js", "file": "wouter.js", "fileHash": "e88c605c", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-FRGL3FG6": {"file": "chunk-FRGL3FG6.js"}, "chunk-EGMLNDVL": {"file": "chunk-EGMLNDVL.js"}, "chunk-YB6LZUMF": {"file": "chunk-YB6LZUMF.js"}, "chunk-F5DG356N": {"file": "chunk-F5DG356N.js"}, "chunk-BGZPITCY": {"file": "chunk-BGZPITCY.js"}, "chunk-2DHEELTQ": {"file": "chunk-2DHEELTQ.js"}, "chunk-D2MBSGVB": {"file": "chunk-D2MBSGVB.js"}, "chunk-C6CIBY4I": {"file": "chunk-C6CIBY4I.js"}, "chunk-5S42QOQO": {"file": "chunk-5S42QOQO.js"}, "chunk-WERSD76P": {"file": "chunk-WERSD76P.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}