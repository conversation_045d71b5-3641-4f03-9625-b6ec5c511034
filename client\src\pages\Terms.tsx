import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTranslation, type Language } from "@/lib/i18n";
import { SEOHead } from "@/components/SEOHead";
import { FileText, AlertTriangle, CreditCard, Shield, Users, Gavel, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface TermsProps {
  language: Language;
}

export default function Terms({ language }: TermsProps) {
  const { t } = useTranslation(language);

  const sections = [
    {
      id: "acceptance",
      title: t("terms.acceptance.title"),
      icon: <FileText className="h-6 w-6" />,
      content: t("terms.acceptance.content")
    },
    {
      id: "service",
      title: t("terms.service.title"),
      icon: <Shield className="h-6 w-6" />,
      content: t("terms.service.content")
    },
    {
      id: "usage",
      title: t("terms.usage.title"),
      icon: <AlertTriangle className="h-6 w-6" />,
      content: t("terms.usage.content")
    },
    {
      id: "intellectual-property",
      title: t("terms.intellectual.title"),
      icon: <Gavel className="h-6 w-6" />,
      content: t("terms.intellectual.content")
    },
    {
      id: "privacy",
      title: t("terms.privacy.title"),
      icon: <Shield className="h-6 w-6" />,
      content: t("terms.privacy.content")
    },
    {
      id: "termination",
      title: t("terms.termination.title"),
      icon: <Users className="h-6 w-6" />,
      content: t("terms.termination.content")
    },

  ];

  return (
    <>
      <SEOHead
        title={`${t("terms.page.title")} - Remove.bg`}
        description={t("terms.page.description")}
        language={language}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Back Button */}
            <div className="mb-8">
              <Link href="/">
                <Button variant="ghost" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  {t("terms.back_home")}
                </Button>
              </Link>
            </div>
            <div className="text-center mb-16">
              <div className="gradient-bg rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <FileText className="h-10 w-10 text-white" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                {t("terms.page.title")}
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t("terms.page.description")}
              </p>
              <div className="mt-6 text-sm text-gray-500">
                <p>{t("terms.last_updated")}</p>
                <p>{t("terms.effective_date")}</p>
              </div>
            </div>
          </div>
        </section>

        {/* Terms Sections */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Quick Summary */}
            <Card className="mb-12 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl gradient-text">{t("terms.summary.title")}</CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600 space-y-4">
                <p>
                  {t("terms.summary.description")}
                </p>
              </CardContent>
            </Card>

            {/* Detailed Sections */}
            <div className="space-y-8">
              {sections.map((section, index) => (
                <Card key={section.id} className="shadow-lg">
                  <CardHeader>
                    <div className="flex items-center mb-4">
                      <div className="gradient-bg rounded-lg p-3 text-white mr-4">
                        {section.icon}
                      </div>
                      <CardTitle className="text-xl text-gray-900">
                        {index + 1}. {section.title}
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-gray-600 whitespace-pre-line leading-relaxed">
                      {section.content}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Contact Information */}
            <Card className="mt-12 shadow-lg border-l-4 border-purple-500">
              <CardHeader>
                <CardTitle className="text-xl text-purple-700">
                  Questions About These Terms?
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  If you have any questions about these Terms of Service, please contact us:
                </p>
                <div className="mt-4 space-y-2">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Address:</strong> Remove.bg Legal Team<br />
                  123 AI Technology Street<br />
                  San Francisco, CA 94105<br />
                  United States</p>
                </div>
                <p className="mt-4 text-sm">
                  We will respond to your inquiry within 5 business days.
                </p>
              </CardContent>
            </Card>

            {/* Governing Law */}
            <Card className="mt-8 shadow-lg border-l-4 border-blue-500">
              <CardHeader>
                <CardTitle className="text-xl text-blue-700">
                  Governing Law
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  These Terms shall be interpreted and governed by the laws of the State of California, United States, 
                  without regard to its conflict of law provisions.
                </p>
                <p className="mt-4">
                  Any disputes arising from these Terms or your use of the Service will be resolved through binding 
                  arbitration in accordance with the rules of the American Arbitration Association.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </>
  );
}
