import { useState, useEffect } from "react";
import { Switch, Route, useLocation } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { CookieConsent } from "@/components/CookieConsent";
import { SEOHead } from "@/components/SEOHead";
import { type Language } from "@/lib/i18n";

// Pages
import Home from "@/pages/Home";
import Tools from "@/pages/Tools";
import Blog from "@/pages/Blog";
import Privacy from "@/pages/Privacy";
import Terms from "@/pages/Terms";
import Help from "@/pages/Help";
import Contact from "@/pages/Contact";
import Status from "@/pages/Status";
import Feedback from "@/pages/Feedback";
import Cookies from "@/pages/Cookies";
import Imprint from "@/pages/Imprint";
import NotFound from "@/pages/not-found";

function Router({ language, onLanguageChange }: { language: Language; onLanguageChange: (lang: Language) => void }) {
  const [location] = useLocation();
  
  // Extract language from URL path (e.g., /en/, /zh/, etc.)
  const pathLanguage = location.split('/')[1] as Language;
  
  useEffect(() => {
    if (['en', 'zh', 'es', 'fr', 'de'].includes(pathLanguage) && pathLanguage !== language) {
      onLanguageChange(pathLanguage);
    }
  }, [pathLanguage, language, onLanguageChange]);

  return (
    <>
      <SEOHead language={language} />
      <div className="min-h-screen flex flex-col">
        <Header language={language} onLanguageChange={onLanguageChange} />
        <main className="flex-1">
          <Switch>
            {/* Language-specific home routes */}
            <Route path="/" component={() => <Home language={language} />} />
            <Route path="/en" component={() => <Home language={language} />} />
            <Route path="/zh" component={() => <Home language={language} />} />
            <Route path="/es" component={() => <Home language={language} />} />
            <Route path="/fr" component={() => <Home language={language} />} />
            <Route path="/de" component={() => <Home language={language} />} />

            {/* Feature pages - both with and without language prefix */}
            <Route path="/tools" component={() => <Tools language={language} />} />
            <Route path="/:lang/tools" component={() => <Tools language={language} />} />
            <Route path="/blog" component={() => <Blog language={language} />} />
            <Route path="/:lang/blog" component={() => <Blog language={language} />} />

            {/* Support pages - both with and without language prefix */}
            <Route path="/help" component={() => <Help language={language} />} />
            <Route path="/:lang/help" component={() => <Help language={language} />} />
            <Route path="/contact" component={() => <Contact language={language} />} />
            <Route path="/:lang/contact" component={() => <Contact language={language} />} />
            <Route path="/status" component={() => <Status language={language} />} />
            <Route path="/:lang/status" component={() => <Status language={language} />} />
            <Route path="/feedback" component={() => <Feedback language={language} />} />
            <Route path="/:lang/feedback" component={() => <Feedback language={language} />} />

            {/* Legal pages - both with and without language prefix */}
            <Route path="/privacy" component={() => <Privacy language={language} />} />
            <Route path="/:lang/privacy" component={() => <Privacy language={language} />} />
            <Route path="/terms" component={() => <Terms language={language} />} />
            <Route path="/:lang/terms" component={() => <Terms language={language} />} />
            <Route path="/cookies" component={() => <Cookies language={language} />} />
            <Route path="/:lang/cookies" component={() => <Cookies language={language} />} />
            <Route path="/imprint" component={() => <Imprint language={language} />} />
            <Route path="/:lang/imprint" component={() => <Imprint language={language} />} />

            {/* Additional routes */}
            <Route path="/enterprise" component={() => <div className="container mx-auto py-16 text-center"><h1 className="text-4xl font-bold">Enterprise Solutions - Coming Soon</h1></div>} />
            <Route path="/:lang/enterprise" component={() => <div className="container mx-auto py-16 text-center"><h1 className="text-4xl font-bold">Enterprise Solutions - Coming Soon</h1></div>} />

            {/* Fallback to 404 */}
            <Route component={NotFound} />
          </Switch>
        </main>
        <Footer language={language} />
        <CookieConsent language={language} />
      </div>
    </>
  );
}

function App() {
  const [language, setLanguage] = useState<Language>('en');

  // Initialize language from localStorage or browser language
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && ['en', 'zh', 'es', 'fr', 'de'].includes(savedLanguage)) {
      setLanguage(savedLanguage);
    } else {
      // Detect browser language
      const browserLang = navigator.language.split('-')[0] as Language;
      if (['en', 'zh', 'es', 'fr', 'de'].includes(browserLang)) {
        setLanguage(browserLang);
      }
    }
  }, []);

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);
    
    // Update URL to include language prefix
    const currentPath = window.location.pathname;
    const pathWithoutLang = currentPath.replace(/^\/(en|zh|es|fr|de)/, '') || '/';
    const newPath = newLanguage === 'en' ? pathWithoutLang : `/${newLanguage}${pathWithoutLang}`;
    window.history.replaceState({}, '', newPath);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router language={language} onLanguageChange={handleLanguageChange} />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
